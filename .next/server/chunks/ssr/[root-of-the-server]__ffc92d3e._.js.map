{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/MainLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/MainLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatXP(xp: number): string {\n  if (xp >= 1000000) {\n    return `${(xp / 1000000).toFixed(1)}M`;\n  }\n  if (xp >= 1000) {\n    return `${(xp / 1000).toFixed(1)}K`;\n  }\n  return xp.toString();\n}\n\nexport function calculateLevel(xp: number): number {\n  // Level formula: level = floor(sqrt(xp / 100))\n  return Math.floor(Math.sqrt(xp / 100));\n}\n\nexport function getXPForLevel(level: number): number {\n  // XP needed for a specific level\n  return level * level * 100;\n}\n\nexport function getXPForNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  return getXPForLevel(currentLevel + 1);\n}\n\nexport function getProgressToNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  const currentLevelXP = getXPForLevel(currentLevel);\n  const nextLevelXP = getXPForLevel(currentLevel + 1);\n  const progress = (currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP);\n  return Math.max(0, Math.min(1, progress));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = seconds % 60;\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function getLanguageIcon(language: string): string {\n  const icons: Record<string, string> = {\n    python: '🐍',\n    javascript: '🟨',\n    typescript: '🔷',\n    csharp: '🔷',\n    nodejs: '🟢',\n  };\n  return icons[language.toLowerCase()] || '📝';\n}\n\nexport function getDifficultyColor(difficulty: 'beginner' | 'intermediate' | 'advanced'): string {\n  const colors = {\n    beginner: 'text-success',\n    intermediate: 'text-warning',\n    advanced: 'text-destructive',\n  };\n  return colors[difficulty];\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,SAAS,EAAU;IACjC,IAAI,MAAM,SAAS;QACjB,OAAO,GAAG,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,MAAM,MAAM;QACd,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,OAAO,GAAG,QAAQ;AACpB;AAEO,SAAS,eAAe,EAAU;IACvC,+CAA+C;IAC/C,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK;AACnC;AAEO,SAAS,cAAc,KAAa;IACzC,iCAAiC;IACjC,OAAO,QAAQ,QAAQ;AACzB;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,eAAe,eAAe;IACpC,OAAO,cAAc,eAAe;AACtC;AAEO,SAAS,uBAAuB,SAAiB;IACtD,MAAM,eAAe,eAAe;IACpC,MAAM,iBAAiB,cAAc;IACrC,MAAM,cAAc,cAAc,eAAe;IACjD,MAAM,WAAW,CAAC,YAAY,cAAc,IAAI,CAAC,cAAc,cAAc;IAC7E,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC1G;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,QAAgC;QACpC,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,KAAK,CAAC,SAAS,WAAW,GAAG,IAAI;AAC1C;AAEO,SAAS,mBAAmB,UAAoD;IACrF,MAAM,SAAS;QACb,UAAU;QACV,cAAc;QACd,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,WAAW;AAC3B;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 glow-primary\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-border bg-transparent hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        glass: \"glass hover:glass-strong border border-border/40 hover:glow-primary\",\n        gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 glow-primary\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,2PACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,gNAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    variant?: 'default' | 'glass' | 'glass-strong' | 'gradient';\n  }\n>(({ className, variant = 'default', ...props }, ref) => {\n  const variantClasses = {\n    default: 'bg-card text-card-foreground border border-border',\n    glass: 'glass',\n    'glass-strong': 'glass-strong',\n    gradient: 'bg-gradient-to-br from-card via-card to-muted border border-border/50',\n  };\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg shadow-sm',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    />\n  );\n});\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,gNAAK,CAAC,UAAU,CAK3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/C,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,wBACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;;;;;;AAGf;AACA,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,gNAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,gNAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,gNAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,gNAAK,CAAC,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,gNAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number;\n  max?: number;\n  variant?: 'default' | 'gradient' | 'glow';\n  size?: 'sm' | 'default' | 'lg';\n  showLabel?: boolean;\n  label?: string;\n}\n\nconst Progress = React.forwardRef<HTMLDivElement, ProgressProps>(\n  ({ \n    className, \n    value = 0, \n    max = 100, \n    variant = 'default',\n    size = 'default',\n    showLabel = false,\n    label,\n    ...props \n  }, ref) => {\n    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n    \n    const sizeClasses = {\n      sm: 'h-2',\n      default: 'h-3',\n      lg: 'h-4',\n    };\n\n    const variantClasses = {\n      default: 'bg-primary',\n      gradient: 'bg-gradient-to-r from-primary via-accent to-secondary',\n      glow: 'bg-primary glow-primary',\n    };\n\n    return (\n      <div className=\"w-full space-y-2\">\n        {(showLabel || label) && (\n          <div className=\"flex justify-between items-center text-sm\">\n            {label && <span className=\"text-foreground/70\">{label}</span>}\n            {showLabel && (\n              <span className=\"text-foreground font-medium\">\n                {Math.round(percentage)}%\n              </span>\n            )}\n          </div>\n        )}\n        <div\n          ref={ref}\n          className={cn(\n            'relative w-full overflow-hidden rounded-full bg-muted',\n            sizeClasses[size],\n            className\n          )}\n          {...props}\n        >\n          <div\n            className={cn(\n              'h-full transition-all duration-500 ease-out rounded-full',\n              variantClasses[variant]\n            )}\n            style={{ width: `${percentage}%` }}\n          />\n          {variant === 'glow' && percentage > 0 && (\n            <div\n              className=\"absolute top-0 left-0 h-full bg-primary/30 rounded-full animate-pulse\"\n              style={{ width: `${percentage}%` }}\n            />\n          )}\n        </div>\n      </div>\n    );\n  }\n);\n\nProgress.displayName = 'Progress';\n\nexport { Progress };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAWA,MAAM,yBAAW,gNAAK,CAAC,UAAU,CAC/B,CAAC,EACC,SAAS,EACT,QAAQ,CAAC,EACT,MAAM,GAAG,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,YAAY,KAAK,EACjB,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,CAAC,aAAa,KAAK,mBAClB,8OAAC;gBAAI,WAAU;;oBACZ,uBAAS,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;oBAC/C,2BACC,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAKhC,8OAAC;gBACC,KAAK;gBACL,WAAW,IAAA,yHAAE,EACX,yDACA,WAAW,CAAC,KAAK,EACjB;gBAED,GAAG,KAAK;;kCAET,8OAAC;wBACC,WAAW,IAAA,yHAAE,EACX,4DACA,cAAc,CAAC,QAAQ;wBAEzB,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;oBAElC,YAAY,UAAU,aAAa,mBAClC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;;;;;;;;AAM7C;AAGF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\nimport <PERSON> from 'next/link';\nimport { ArrowRight, Code, Trophy, Users, Zap, BookOpen, Target } from 'lucide-react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Progress } from '@/components/ui/Progress';\n\n// Mock user data for demonstration\nconst mockUser = {\n  username: 'CodeMaster',\n  level: 12,\n  xp: 14500,\n  avatar: undefined,\n};\n\nconst features = [\n  {\n    icon: Code,\n    title: 'Interactive Code Editor',\n    description: 'Write, test, and execute code directly in your browser with real-time feedback.',\n    color: 'text-primary',\n  },\n  {\n    icon: Trophy,\n    title: 'Gamified Learning',\n    description: 'Earn XP, unlock achievements, and collect trophies as you progress through lessons.',\n    color: 'text-secondary',\n  },\n  {\n    icon: Users,\n    title: 'Community Challenges',\n    description: 'Compete with other learners in daily challenges and climb the leaderboards.',\n    color: 'text-accent',\n  },\n  {\n    icon: Zap,\n    title: 'Virtual Pet Companion',\n    description: 'Grow and care for your coding companion that evolves with your learning journey.',\n    color: 'text-destructive',\n  },\n];\n\nconst languages = [\n  { name: 'Python', icon: '🐍', progress: 75, color: 'from-green-500 to-blue-500' },\n  { name: 'C#', icon: '🔷', progress: 45, color: 'from-purple-500 to-pink-500' },\n  { name: 'Node.js', icon: '🟢', progress: 60, color: 'from-green-400 to-emerald-500' },\n];\n\nexport default function Home() {\n  return (\n    <MainLayout user={mockUser} showSidebar={false}>\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Hero Section */}\n        <section className=\"text-center py-20\">\n          <div className=\"space-y-6\">\n            <h1 className=\"text-5xl md:text-7xl font-bold bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent\">\n              Learn to Code\n            </h1>\n            <h2 className=\"text-3xl md:text-4xl font-semibold text-foreground/90\">\n              with <span className=\"text-primary\">VibeCode</span>\n            </h2>\n            <p className=\"text-xl text-foreground/70 max-w-2xl mx-auto leading-relaxed\">\n              Master backend programming through interactive lessons, gamified challenges,\n              and a virtual pet companion that grows with your coding skills.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center pt-8\">\n              <Button size=\"xl\" variant=\"gradient\" asChild>\n                <Link href=\"/courses\" className=\"flex items-center space-x-2\">\n                  <BookOpen className=\"w-5 h-5\" />\n                  <span>Start Learning</span>\n                  <ArrowRight className=\"w-5 h-5\" />\n                </Link>\n              </Button>\n              <Button size=\"xl\" variant=\"glass\" asChild>\n                <Link href=\"/challenges\" className=\"flex items-center space-x-2\">\n                  <Target className=\"w-5 h-5\" />\n                  <span>Try Challenge</span>\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-20\">\n          <div className=\"text-center mb-16\">\n            <h3 className=\"text-3xl font-bold text-foreground mb-4\">\n              Why Choose VibeCode?\n            </h3>\n            <p className=\"text-foreground/70 text-lg max-w-2xl mx-auto\">\n              Experience a new way of learning programming with our innovative features\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {features.map((feature, index) => {\n              const Icon = feature.icon;\n              return (\n                <Card key={index} variant=\"glass\" className=\"hover:glow-primary transition-all duration-300 group\">\n                  <CardHeader className=\"text-center\">\n                    <div className={`w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\n                      <Icon className={`w-6 h-6 ${feature.color}`} />\n                    </div>\n                    <CardTitle className=\"text-lg\">{feature.title}</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-center\">\n                      {feature.description}\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n        </section>\n\n        {/* Languages Section */}\n        <section className=\"py-20\">\n          <div className=\"text-center mb-16\">\n            <h3 className=\"text-3xl font-bold text-foreground mb-4\">\n              Master Backend Technologies\n            </h3>\n            <p className=\"text-foreground/70 text-lg max-w-2xl mx-auto\">\n              Learn the most in-demand backend programming languages with hands-on projects\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {languages.map((lang, index) => (\n              <Card key={index} variant=\"glass-strong\" className=\"hover:glow-accent transition-all duration-300\">\n                <CardHeader className=\"text-center\">\n                  <div className=\"text-4xl mb-4\">{lang.icon}</div>\n                  <CardTitle className=\"text-xl\">{lang.name}</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <Progress\n                    value={lang.progress}\n                    variant=\"gradient\"\n                    showLabel\n                    label=\"Course Progress\"\n                  />\n                  <Button variant=\"outline\" className=\"w-full\">\n                    Continue Learning\n                  </Button>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 text-center\">\n          <Card variant=\"gradient\" className=\"p-12 glow-primary\">\n            <div className=\"space-y-6\">\n              <h3 className=\"text-3xl font-bold text-foreground\">\n                Ready to Start Your Coding Journey?\n              </h3>\n              <p className=\"text-foreground/80 text-lg max-w-2xl mx-auto\">\n                Join thousands of learners who are mastering backend development with VibeCode\n              </p>\n              <Button size=\"xl\" variant=\"glass\" asChild>\n                <Link href=\"/sign-up\" className=\"flex items-center space-x-2\">\n                  <span>Get Started Free</span>\n                  <ArrowRight className=\"w-5 h-5\" />\n                </Link>\n              </Button>\n            </div>\n          </Card>\n        </section>\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,mCAAmC;AACnC,MAAM,WAAW;IACf,UAAU;IACV,OAAO;IACP,IAAI;IACJ,QAAQ;AACV;AAEA,MAAM,WAAW;IACf;QACE,MAAM,0MAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,gNAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,6MAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,uMAAG;QACT,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,YAAY;IAChB;QAAE,MAAM;QAAU,MAAM;QAAM,UAAU;QAAI,OAAO;IAA6B;IAChF;QAAE,MAAM;QAAM,MAAM;QAAM,UAAU;QAAI,OAAO;IAA8B;IAC7E;QAAE,MAAM;QAAW,MAAM;QAAM,UAAU;QAAI,OAAO;IAAgC;CACrF;AAEc,SAAS;IACtB,qBACE,8OAAC,qJAAU;QAAC,MAAM;QAAU,aAAa;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAQ,WAAU;8BACjB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqH;;;;;;0CAGnI,8OAAC;gCAAG,WAAU;;oCAAwD;kDAC/D,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEtC,8OAAC;gCAAE,WAAU;0CAA+D;;;;;;0CAI5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAW,OAAO;kDAC1C,cAAA,8OAAC,uKAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,8OAAC,0NAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;8DACN,8OAAC,gOAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC,4IAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAQ,OAAO;kDACvC,cAAA,8OAAC,uKAAI;4CAAC,MAAK;4CAAc,WAAU;;8DACjC,8OAAC,gNAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQhB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gCACtB,MAAM,OAAO,QAAQ,IAAI;gCACzB,qBACE,8OAAC,wIAAI;oCAAa,SAAQ;oCAAQ,WAAU;;sDAC1C,8OAAC,8IAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAW,CAAC,yKAAyK,CAAC;8DACzL,cAAA,8OAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;8DAE7C,8OAAC,6IAAS;oDAAC,WAAU;8DAAW,QAAQ,KAAK;;;;;;;;;;;;sDAE/C,8OAAC,+IAAW;sDACV,cAAA,8OAAC,mJAAe;gDAAC,WAAU;0DACxB,QAAQ,WAAW;;;;;;;;;;;;mCATf;;;;;4BAcf;;;;;;;;;;;;8BAKJ,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,wIAAI;oCAAa,SAAQ;oCAAe,WAAU;;sDACjD,8OAAC,8IAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DAAiB,KAAK,IAAI;;;;;;8DACzC,8OAAC,6IAAS;oDAAC,WAAU;8DAAW,KAAK,IAAI;;;;;;;;;;;;sDAE3C,8OAAC,+IAAW;4CAAC,WAAU;;8DACrB,8OAAC,gJAAQ;oDACP,OAAO,KAAK,QAAQ;oDACpB,SAAQ;oDACR,SAAS;oDACT,OAAM;;;;;;8DAER,8OAAC,4IAAM;oDAAC,SAAQ;oDAAU,WAAU;8DAAS;;;;;;;;;;;;;mCAZtC;;;;;;;;;;;;;;;;8BAsBjB,8OAAC;oBAAQ,WAAU;8BACjB,cAAA,8OAAC,wIAAI;wBAAC,SAAQ;wBAAW,WAAU;kCACjC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CAGnD,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,8OAAC,4IAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAQ,OAAO;8CACvC,cAAA,8OAAC,uKAAI;wCAAC,MAAK;wCAAW,WAAU;;0DAC9B,8OAAC;0DAAK;;;;;;0DACN,8OAAC,gOAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}