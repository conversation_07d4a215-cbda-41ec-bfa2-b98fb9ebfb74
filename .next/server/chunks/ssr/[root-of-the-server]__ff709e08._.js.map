{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/MainLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/MainLayout.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatXP(xp: number): string {\n  if (xp >= 1000000) {\n    return `${(xp / 1000000).toFixed(1)}M`;\n  }\n  if (xp >= 1000) {\n    return `${(xp / 1000).toFixed(1)}K`;\n  }\n  return xp.toString();\n}\n\nexport function calculateLevel(xp: number): number {\n  // Level formula: level = floor(sqrt(xp / 100))\n  return Math.floor(Math.sqrt(xp / 100));\n}\n\nexport function getXPForLevel(level: number): number {\n  // XP needed for a specific level\n  return level * level * 100;\n}\n\nexport function getXPForNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  return getXPForLevel(currentLevel + 1);\n}\n\nexport function getProgressToNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  const currentLevelXP = getXPForLevel(currentLevel);\n  const nextLevelXP = getXPForLevel(currentLevel + 1);\n  const progress = (currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP);\n  return Math.max(0, Math.min(1, progress));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = seconds % 60;\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function getLanguageIcon(language: string): string {\n  const icons: Record<string, string> = {\n    python: '🐍',\n    javascript: '🟨',\n    typescript: '🔷',\n    csharp: '🔷',\n    nodejs: '🟢',\n  };\n  return icons[language.toLowerCase()] || '📝';\n}\n\nexport function getDifficultyColor(difficulty: 'beginner' | 'intermediate' | 'advanced'): string {\n  const colors = {\n    beginner: 'text-success',\n    intermediate: 'text-warning',\n    advanced: 'text-destructive',\n  };\n  return colors[difficulty];\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,SAAS,EAAU;IACjC,IAAI,MAAM,SAAS;QACjB,OAAO,GAAG,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,MAAM,MAAM;QACd,OAAO,GAAG,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACrC;IACA,OAAO,GAAG,QAAQ;AACpB;AAEO,SAAS,eAAe,EAAU;IACvC,+CAA+C;IAC/C,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK;AACnC;AAEO,SAAS,cAAc,KAAa;IACzC,iCAAiC;IACjC,OAAO,QAAQ,QAAQ;AACzB;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,eAAe,eAAe;IACpC,OAAO,cAAc,eAAe;AACtC;AAEO,SAAS,uBAAuB,SAAiB;IACtD,MAAM,eAAe,eAAe;IACpC,MAAM,iBAAiB,cAAc;IACrC,MAAM,cAAc,cAAc,eAAe;IACjD,MAAM,WAAW,CAAC,YAAY,cAAc,IAAI,CAAC,cAAc,cAAc;IAC7E,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC1G;IACA,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,QAAgC;QACpC,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,KAAK,CAAC,SAAS,WAAW,GAAG,IAAI;AAC1C;AAEO,SAAS,mBAAmB,UAAoD;IACrF,MAAM,SAAS;QACb,UAAU;QACV,cAAc;QACd,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,WAAW;AAC3B;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    variant?: 'default' | 'glass' | 'glass-strong' | 'gradient';\n  }\n>(({ className, variant = 'default', ...props }, ref) => {\n  const variantClasses = {\n    default: 'bg-card text-card-foreground border border-border',\n    glass: 'glass',\n    'glass-strong': 'glass-strong',\n    gradient: 'bg-gradient-to-br from-card via-card to-muted border border-border/50',\n  };\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-lg shadow-sm',\n        variantClasses[variant],\n        className\n      )}\n      {...props}\n    />\n  );\n});\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,gNAAK,CAAC,UAAU,CAK3B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/C,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,gBAAgB;QAChB,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,wBACA,cAAc,CAAC,QAAQ,EACvB;QAED,GAAG,KAAK;;;;;;AAGf;AACA,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,gNAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,gNAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,gNAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,gNAAK,CAAC,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,gNAAK,CAAC,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Progress.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {\n  value?: number;\n  max?: number;\n  variant?: 'default' | 'gradient' | 'glow';\n  size?: 'sm' | 'default' | 'lg';\n  showLabel?: boolean;\n  label?: string;\n}\n\nconst Progress = React.forwardRef<HTMLDivElement, ProgressProps>(\n  ({ \n    className, \n    value = 0, \n    max = 100, \n    variant = 'default',\n    size = 'default',\n    showLabel = false,\n    label,\n    ...props \n  }, ref) => {\n    const percentage = Math.min(Math.max((value / max) * 100, 0), 100);\n    \n    const sizeClasses = {\n      sm: 'h-2',\n      default: 'h-3',\n      lg: 'h-4',\n    };\n\n    const variantClasses = {\n      default: 'bg-primary',\n      gradient: 'bg-gradient-to-r from-primary via-accent to-secondary',\n      glow: 'bg-primary glow-primary',\n    };\n\n    return (\n      <div className=\"w-full space-y-2\">\n        {(showLabel || label) && (\n          <div className=\"flex justify-between items-center text-sm\">\n            {label && <span className=\"text-foreground/70\">{label}</span>}\n            {showLabel && (\n              <span className=\"text-foreground font-medium\">\n                {Math.round(percentage)}%\n              </span>\n            )}\n          </div>\n        )}\n        <div\n          ref={ref}\n          className={cn(\n            'relative w-full overflow-hidden rounded-full bg-muted',\n            sizeClasses[size],\n            className\n          )}\n          {...props}\n        >\n          <div\n            className={cn(\n              'h-full transition-all duration-500 ease-out rounded-full',\n              variantClasses[variant]\n            )}\n            style={{ width: `${percentage}%` }}\n          />\n          {variant === 'glow' && percentage > 0 && (\n            <div\n              className=\"absolute top-0 left-0 h-full bg-primary/30 rounded-full animate-pulse\"\n              style={{ width: `${percentage}%` }}\n            />\n          )}\n        </div>\n      </div>\n    );\n  }\n);\n\nProgress.displayName = 'Progress';\n\nexport { Progress };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAWA,MAAM,yBAAW,gNAAK,CAAC,UAAU,CAC/B,CAAC,EACC,SAAS,EACT,QAAQ,CAAC,EACT,MAAM,GAAG,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,YAAY,KAAK,EACjB,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,AAAC,QAAQ,MAAO,KAAK,IAAI;IAE9D,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,UAAU;QACV,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,CAAC,aAAa,KAAK,mBAClB,8OAAC;gBAAI,WAAU;;oBACZ,uBAAS,8OAAC;wBAAK,WAAU;kCAAsB;;;;;;oBAC/C,2BACC,8OAAC;wBAAK,WAAU;;4BACb,KAAK,KAAK,CAAC;4BAAY;;;;;;;;;;;;;0BAKhC,8OAAC;gBACC,KAAK;gBACL,WAAW,IAAA,yHAAE,EACX,yDACA,WAAW,CAAC,KAAK,EACjB;gBAED,GAAG,KAAK;;kCAET,8OAAC;wBACC,WAAW,IAAA,yHAAE,EACX,4DACA,cAAc,CAAC,QAAQ;wBAEzB,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;oBAElC,YAAY,UAAU,aAAa,mBAClC,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,OAAO,GAAG,WAAW,CAAC,CAAC;wBAAC;;;;;;;;;;;;;;;;;;AAM7C;AAGF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90 glow-primary\",\n        destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline: \"border border-border bg-transparent hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/90\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        glass: \"glass hover:glass-strong border border-border/40 hover:glow-primary\",\n        gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90 glow-primary\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,2PACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,gNAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/app/dashboard/page.tsx"], "sourcesContent": ["import React from 'react';\nimport { BookOpen, Trophy, Target, Calendar, TrendingUp, Zap } from 'lucide-react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Progress } from '@/components/ui/Progress';\nimport { Button } from '@/components/ui/Button';\n\n// Mock user data\nconst mockUser = {\n  username: 'CodeMaster',\n  level: 12,\n  xp: 14500,\n  avatar: undefined,\n};\n\nconst stats = [\n  {\n    title: 'Lessons Completed',\n    value: '47',\n    change: '+12%',\n    icon: BookOpen,\n    color: 'text-primary',\n  },\n  {\n    title: 'Current Streak',\n    value: '7 days',\n    change: '+2 days',\n    icon: Zap,\n    color: 'text-secondary',\n  },\n  {\n    title: 'Achievements',\n    value: '23',\n    change: '+3 new',\n    icon: Trophy,\n    color: 'text-accent',\n  },\n  {\n    title: 'Rank',\n    value: '#156',\n    change: '+24',\n    icon: Target,\n    color: 'text-destructive',\n  },\n];\n\nconst recentActivity = [\n  {\n    title: 'Completed Python Basics - Variables',\n    time: '2 hours ago',\n    xp: '+50 XP',\n    type: 'lesson',\n  },\n  {\n    title: 'Earned \"First Steps\" Achievement',\n    time: '3 hours ago',\n    xp: '+100 XP',\n    type: 'achievement',\n  },\n  {\n    title: 'Solved Daily Challenge',\n    time: '1 day ago',\n    xp: '+75 XP',\n    type: 'challenge',\n  },\n];\n\nconst currentCourses = [\n  {\n    title: 'Python Fundamentals',\n    progress: 75,\n    nextLesson: 'Functions and Scope',\n    language: 'Python',\n    icon: '🐍',\n  },\n  {\n    title: 'C# Basics',\n    progress: 45,\n    nextLesson: 'Object-Oriented Programming',\n    language: 'C#',\n    icon: '🔷',\n  },\n  {\n    title: 'Node.js Development',\n    progress: 30,\n    nextLesson: 'Express.js Fundamentals',\n    language: 'Node.js',\n    icon: '🟢',\n  },\n];\n\nexport default function Dashboard() {\n  return (\n    <MainLayout user={mockUser}>\n      <div className=\"space-y-8\">\n        {/* Welcome Section */}\n        <div className=\"space-y-2\">\n          <h1 className=\"text-3xl font-bold text-foreground\">\n            Welcome back, {mockUser.username}! 👋\n          </h1>\n          <p className=\"text-foreground/70\">\n            Ready to continue your coding journey? Let's see what you can accomplish today.\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {stats.map((stat, index) => {\n            const Icon = stat.icon;\n            return (\n              <Card key={index} variant=\"glass\" className=\"hover:glow-primary transition-all duration-300\">\n                <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                  <CardTitle className=\"text-sm font-medium text-foreground/70\">\n                    {stat.title}\n                  </CardTitle>\n                  <Icon className={`w-4 h-4 ${stat.color}`} />\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold text-foreground\">{stat.value}</div>\n                  <p className=\"text-xs text-primary font-medium\">\n                    {stat.change} from last week\n                  </p>\n                </CardContent>\n              </Card>\n            );\n          })}\n        </div>\n\n        <div className=\"grid lg:grid-cols-3 gap-8\">\n          {/* Current Courses */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <Card variant=\"glass-strong\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <BookOpen className=\"w-5 h-5 text-primary\" />\n                  <span>Continue Learning</span>\n                </CardTitle>\n                <CardDescription>\n                  Pick up where you left off in your courses\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {currentCourses.map((course, index) => (\n                  <div key={index} className=\"glass p-4 rounded-lg hover:glow-accent transition-all duration-300\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <span className=\"text-2xl\">{course.icon}</span>\n                        <div>\n                          <h4 className=\"font-semibold text-foreground\">{course.title}</h4>\n                          <p className=\"text-sm text-foreground/60\">Next: {course.nextLesson}</p>\n                        </div>\n                      </div>\n                      <Button size=\"sm\" variant=\"outline\">\n                        Continue\n                      </Button>\n                    </div>\n                    <Progress \n                      value={course.progress} \n                      variant=\"gradient\" \n                      showLabel \n                    />\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Recent Activity */}\n          <div className=\"space-y-6\">\n            <Card variant=\"glass-strong\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <TrendingUp className=\"w-5 h-5 text-accent\" />\n                  <span>Recent Activity</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {recentActivity.map((activity, index) => (\n                  <div key={index} className=\"flex items-start space-x-3 p-3 glass rounded-lg\">\n                    <div className={`w-2 h-2 rounded-full mt-2 ${\n                      activity.type === 'lesson' ? 'bg-primary' :\n                      activity.type === 'achievement' ? 'bg-secondary' : 'bg-accent'\n                    }`} />\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium text-foreground truncate\">\n                        {activity.title}\n                      </p>\n                      <div className=\"flex items-center justify-between mt-1\">\n                        <p className=\"text-xs text-foreground/60\">{activity.time}</p>\n                        <span className=\"text-xs font-medium text-primary\">{activity.xp}</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </CardContent>\n            </Card>\n\n            {/* Daily Challenge */}\n            <Card variant=\"glass-strong\" className=\"glow-secondary\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Calendar className=\"w-5 h-5 text-secondary\" />\n                  <span>Daily Challenge</span>\n                </CardTitle>\n                <CardDescription>\n                  Complete today's challenge for bonus XP!\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"glass p-4 rounded-lg\">\n                    <h4 className=\"font-semibold text-foreground mb-2\">\n                      Python List Comprehensions\n                    </h4>\n                    <p className=\"text-sm text-foreground/70 mb-3\">\n                      Create a list comprehension that filters even numbers from 1 to 100.\n                    </p>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-secondary font-medium\">+150 XP</span>\n                      <Button size=\"sm\" variant=\"gradient\">\n                        Start Challenge\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,iBAAiB;AACjB,MAAM,WAAW;IACf,UAAU;IACV,OAAO;IACP,IAAI;IACJ,QAAQ;AACV;AAEA,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,0NAAQ;QACd,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,uMAAG;QACT,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,gNAAM;QACZ,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM,gNAAM;QACZ,OAAO;IACT;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,MAAM;QACN,IAAI;QACJ,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,IAAI;QACJ,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;QACN,IAAI;QACJ,MAAM;IACR;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,UAAU;QACV,YAAY;QACZ,UAAU;QACV,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,qJAAU;QAAC,MAAM;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAqC;gCAClC,SAAS,QAAQ;gCAAC;;;;;;;sCAEnC,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM;wBAChB,MAAM,OAAO,KAAK,IAAI;wBACtB,qBACE,8OAAC,wIAAI;4BAAa,SAAQ;4BAAQ,WAAU;;8CAC1C,8OAAC,8IAAU;oCAAC,WAAU;;sDACpB,8OAAC,6IAAS;4CAAC,WAAU;sDAClB,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;8CAE1C,8OAAC,+IAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAsC,KAAK,KAAK;;;;;;sDAC/D,8OAAC;4CAAE,WAAU;;gDACV,KAAK,MAAM;gDAAC;;;;;;;;;;;;;;2BAVR;;;;;oBAef;;;;;;8BAGF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAI;gCAAC,SAAQ;;kDACZ,8OAAC,8IAAU;;0DACT,8OAAC,6IAAS;gDAAC,WAAU;;kEACnB,8OAAC,0NAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,mJAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,+IAAW;wCAAC,WAAU;kDACpB,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAY,OAAO,IAAI;;;;;;kFACvC,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAiC,OAAO,KAAK;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;;oFAA6B;oFAAO,OAAO,UAAU;;;;;;;;;;;;;;;;;;;0EAGtE,8OAAC,4IAAM;gEAAC,MAAK;gEAAK,SAAQ;0EAAU;;;;;;;;;;;;kEAItC,8OAAC,gJAAQ;wDACP,OAAO,OAAO,QAAQ;wDACtB,SAAQ;wDACR,SAAS;;;;;;;+CAhBH;;;;;;;;;;;;;;;;;;;;;sCAyBlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wIAAI;oCAAC,SAAQ;;sDACZ,8OAAC,8IAAU;sDACT,cAAA,8OAAC,6IAAS;gDAAC,WAAU;;kEACnB,8OAAC,gOAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC,+IAAW;4CAAC,WAAU;sDACpB,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAW,CAAC,0BAA0B,EACzC,SAAS,IAAI,KAAK,WAAW,eAC7B,SAAS,IAAI,KAAK,gBAAgB,iBAAiB,aACnD;;;;;;sEACF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EACV,SAAS,KAAK;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAA8B,SAAS,IAAI;;;;;;sFACxD,8OAAC;4EAAK,WAAU;sFAAoC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;mDAX3D;;;;;;;;;;;;;;;;8CAoBhB,8OAAC,wIAAI;oCAAC,SAAQ;oCAAe,WAAU;;sDACrC,8OAAC,8IAAU;;8DACT,8OAAC,6IAAS;oDAAC,WAAU;;sEACnB,8OAAC,sNAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC,mJAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,+IAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEAGnD,8OAAC;4DAAE,WAAU;sEAAkC;;;;;;sEAG/C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAqC;;;;;;8EACrD,8OAAC,4IAAM;oEAAC,MAAK;oEAAK,SAAQ;8EAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa3D", "debugId": null}}]}