{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { User, Code, Trophy, BookOpen, Settings, LogOut } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface HeaderProps {\n  user?: {\n    username: string;\n    level: number;\n    xp: number;\n    avatar?: string;\n  };\n}\n\nexport default function Header({ user }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border/40 glass-strong\">\n      <div className=\"container mx-auto px-4 h-16 flex items-center justify-between\">\n        {/* Logo and Brand */}\n        <Link href=\"/\" className=\"flex items-center space-x-2 hover:opacity-80 transition-opacity\">\n          <div className=\"w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center\">\n            <Code className=\"w-5 h-5 text-background\" />\n          </div>\n          <span className=\"text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n            VibeCode\n          </span>\n        </Link>\n\n        {/* Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6\">\n          <Link \n            href=\"/learn\" \n            className=\"flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors\"\n          >\n            <BookOpen className=\"w-4 h-4\" />\n            <span>Learn</span>\n          </Link>\n          <Link \n            href=\"/challenges\" \n            className=\"flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors\"\n          >\n            <Trophy className=\"w-4 h-4\" />\n            <span>Challenges</span>\n          </Link>\n          <Link \n            href=\"/leaderboard\" \n            className=\"flex items-center space-x-2 text-foreground/80 hover:text-primary transition-colors\"\n          >\n            <Trophy className=\"w-4 h-4\" />\n            <span>Leaderboard</span>\n          </Link>\n        </nav>\n\n        {/* User Section */}\n        <div className=\"flex items-center space-x-4\">\n          {user ? (\n            <div className=\"flex items-center space-x-3\">\n              {/* XP and Level Display */}\n              <div className=\"hidden sm:flex items-center space-x-2 glass px-3 py-1 rounded-full\">\n                <div className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></div>\n                <span className=\"text-sm font-medium text-foreground\">\n                  Level {user.level}\n                </span>\n                <span className=\"text-xs text-foreground/60\">\n                  {user.xp.toLocaleString()} XP\n                </span>\n              </div>\n\n              {/* User Avatar and Menu */}\n              <div className=\"relative group\">\n                <button className=\"flex items-center space-x-2 glass px-3 py-2 rounded-full hover:glow-primary transition-all duration-200\">\n                  {user.avatar ? (\n                    <img \n                      src={user.avatar} \n                      alt={user.username}\n                      className=\"w-6 h-6 rounded-full\"\n                    />\n                  ) : (\n                    <div className=\"w-6 h-6 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center\">\n                      <User className=\"w-4 h-4 text-background\" />\n                    </div>\n                  )}\n                  <span className=\"text-sm font-medium text-foreground hidden sm:block\">\n                    {user.username}\n                  </span>\n                </button>\n\n                {/* Dropdown Menu */}\n                <div className=\"absolute right-0 top-full mt-2 w-48 glass-strong rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"p-2\">\n                    <Link \n                      href=\"/profile\" \n                      className=\"flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-primary/10 transition-colors\"\n                    >\n                      <User className=\"w-4 h-4\" />\n                      <span>Profile</span>\n                    </Link>\n                    <Link \n                      href=\"/settings\" \n                      className=\"flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-primary/10 transition-colors\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                      <span>Settings</span>\n                    </Link>\n                    <hr className=\"my-2 border-border/40\" />\n                    <button className=\"flex items-center space-x-2 px-3 py-2 rounded-md hover:bg-destructive/10 transition-colors w-full text-left text-destructive\">\n                      <LogOut className=\"w-4 h-4\" />\n                      <span>Sign Out</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <Link \n                href=\"/sign-in\" \n                className=\"px-4 py-2 text-foreground/80 hover:text-primary transition-colors\"\n              >\n                Sign In\n              </Link>\n              <Link \n                href=\"/sign-up\" \n                className=\"px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium\"\n              >\n                Get Started\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAgBe,SAAS,OAAO,KAAqB;QAArB,EAAE,IAAI,EAAe,GAArB;IAC7B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,0KAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6MAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAK,WAAU;sCAA0F;;;;;;;;;;;;8BAM5G,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,6NAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,mNAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;8BACZ,qBACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;;4CAAsC;4CAC7C,KAAK,KAAK;;;;;;;kDAEnB,6LAAC;wCAAK,WAAU;;4CACb,KAAK,EAAE,CAAC,cAAc;4CAAG;;;;;;;;;;;;;0CAK9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;;4CACf,KAAK,MAAM,iBACV,6LAAC;gDACC,KAAK,KAAK,MAAM;gDAChB,KAAK,KAAK,QAAQ;gDAClB,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,6MAAI;oDAAC,WAAU;;;;;;;;;;;0DAGpB,6LAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,0KAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,6MAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC,0KAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,yNAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDAAG,WAAU;;;;;;8DACd,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,uNAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAOhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAvHwB", "debugId": null}}, {"offset": {"line": 384, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatXP(xp: number): string {\n  if (xp >= 1000000) {\n    return `${(xp / 1000000).toFixed(1)}M`;\n  }\n  if (xp >= 1000) {\n    return `${(xp / 1000).toFixed(1)}K`;\n  }\n  return xp.toString();\n}\n\nexport function calculateLevel(xp: number): number {\n  // Level formula: level = floor(sqrt(xp / 100))\n  return Math.floor(Math.sqrt(xp / 100));\n}\n\nexport function getXPForLevel(level: number): number {\n  // XP needed for a specific level\n  return level * level * 100;\n}\n\nexport function getXPForNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  return getXPForLevel(currentLevel + 1);\n}\n\nexport function getProgressToNextLevel(currentXP: number): number {\n  const currentLevel = calculateLevel(currentXP);\n  const currentLevelXP = getXPForLevel(currentLevel);\n  const nextLevelXP = getXPForLevel(currentLevel + 1);\n  const progress = (currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP);\n  return Math.max(0, Math.min(1, progress));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function formatTime(seconds: number): string {\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n  const remainingSeconds = seconds % 60;\n\n  if (hours > 0) {\n    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n}\n\nexport function getLanguageIcon(language: string): string {\n  const icons: Record<string, string> = {\n    python: '🐍',\n    javascript: '🟨',\n    typescript: '🔷',\n    csharp: '🔷',\n    nodejs: '🟢',\n  };\n  return icons[language.toLowerCase()] || '📝';\n}\n\nexport function getDifficultyColor(difficulty: 'beginner' | 'intermediate' | 'advanced'): string {\n  const colors = {\n    beginner: 'text-success',\n    intermediate: 'text-warning',\n    advanced: 'text-destructive',\n  };\n  return colors[difficulty];\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,SAAS,EAAU;IACjC,IAAI,MAAM,SAAS;QACjB,OAAO,AAAC,GAA4B,OAA1B,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,IAAG;IACtC;IACA,IAAI,MAAM,MAAM;QACd,OAAO,AAAC,GAAyB,OAAvB,CAAC,KAAK,IAAI,EAAE,OAAO,CAAC,IAAG;IACnC;IACA,OAAO,GAAG,QAAQ;AACpB;AAEO,SAAS,eAAe,EAAU;IACvC,+CAA+C;IAC/C,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK;AACnC;AAEO,SAAS,cAAc,KAAa;IACzC,iCAAiC;IACjC,OAAO,QAAQ,QAAQ;AACzB;AAEO,SAAS,kBAAkB,SAAiB;IACjD,MAAM,eAAe,eAAe;IACpC,OAAO,cAAc,eAAe;AACtC;AAEO,SAAS,uBAAuB,SAAiB;IACtD,MAAM,eAAe,eAAe;IACpC,MAAM,iBAAiB,cAAc;IACrC,MAAM,cAAc,cAAc,eAAe;IACjD,MAAM,WAAW,CAAC,YAAY,cAAc,IAAI,CAAC,cAAc,cAAc;IAC7E,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AACjC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,OAAe;IACxC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;IAC9C,MAAM,mBAAmB,UAAU;IAEnC,IAAI,QAAQ,GAAG;QACb,OAAO,AAAC,GAAW,OAAT,OAAM,KAA0C,OAAvC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAK,KAAgD,OAA7C,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACpG;IACA,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAgD,OAA7C,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;AAC/D;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,QAAgC;QACpC,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;IACV;IACA,OAAO,KAAK,CAAC,SAAS,WAAW,GAAG,IAAI;AAC1C;AAEO,SAAS,mBAAmB,UAAoD;IACrF,MAAM,SAAS;QACb,UAAU;QACV,cAAc;QACd,UAAU;IACZ;IACA,OAAO,MAAM,CAAC,WAAW;AAC3B;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  BookOpen, \n  Code, \n  Trophy, \n  Target, \n  Users, \n  Heart, \n  Star,\n  TrendingUp,\n  Calendar,\n  Award\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface SidebarProps {\n  isOpen?: boolean;\n  onClose?: () => void;\n}\n\nconst navigationItems = [\n  {\n    title: 'Learning',\n    items: [\n      { name: 'Dashboard', href: '/dashboard', icon: TrendingUp },\n      { name: 'Courses', href: '/courses', icon: BookOpen },\n      { name: 'Code Editor', href: '/editor', icon: Code },\n      { name: 'Daily Challenge', href: '/daily-challenge', icon: Calendar },\n    ]\n  },\n  {\n    title: 'Gamification',\n    items: [\n      { name: 'Achievements', href: '/achievements', icon: Award },\n      { name: 'Trophies', href: '/trophies', icon: Trophy },\n      { name: 'Collections', href: '/collections', icon: Star },\n      { name: 'Virtual Pet', href: '/pet', icon: Heart },\n    ]\n  },\n  {\n    title: 'Community',\n    items: [\n      { name: 'Leaderboard', href: '/leaderboard', icon: Target },\n      { name: 'Community', href: '/community', icon: Users },\n    ]\n  }\n];\n\nexport default function Sidebar({ isOpen = true, onClose }: SidebarProps) {\n  const pathname = usePathname();\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <aside className={cn(\n        \"fixed left-0 top-16 h-[calc(100vh-4rem)] w-64 glass-strong border-r border-border/40 z-50 transition-transform duration-300 lg:translate-x-0\",\n        isOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      )}>\n        <div className=\"flex flex-col h-full\">\n          {/* Navigation */}\n          <nav className=\"flex-1 p-4 space-y-6 overflow-y-auto\">\n            {navigationItems.map((section) => (\n              <div key={section.title}>\n                <h3 className=\"text-xs font-semibold text-foreground/60 uppercase tracking-wider mb-3\">\n                  {section.title}\n                </h3>\n                <ul className=\"space-y-1\">\n                  {section.items.map((item) => {\n                    const isActive = pathname === item.href;\n                    const Icon = item.icon;\n                    \n                    return (\n                      <li key={item.name}>\n                        <Link\n                          href={item.href}\n                          onClick={onClose}\n                          className={cn(\n                            \"flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group\",\n                            isActive \n                              ? \"bg-primary/20 text-primary border border-primary/30 glow-primary\" \n                              : \"text-foreground/70 hover:text-primary hover:bg-primary/10\"\n                          )}\n                        >\n                          <Icon className={cn(\n                            \"w-5 h-5 transition-colors\",\n                            isActive ? \"text-primary\" : \"text-foreground/50 group-hover:text-primary\"\n                          )} />\n                          <span className=\"font-medium\">{item.name}</span>\n                          {isActive && (\n                            <div className=\"ml-auto w-2 h-2 bg-primary rounded-full animate-pulse\" />\n                          )}\n                        </Link>\n                      </li>\n                    );\n                  })}\n                </ul>\n              </div>\n            ))}\n          </nav>\n\n          {/* Progress Summary */}\n          <div className=\"p-4 border-t border-border/40\">\n            <div className=\"glass p-4 rounded-lg\">\n              <h4 className=\"text-sm font-semibold text-foreground mb-3\">Today's Progress</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-foreground/70\">Lessons</span>\n                  <span className=\"text-primary font-medium\">3/5</span>\n                </div>\n                <div className=\"w-full bg-muted rounded-full h-2\">\n                  <div className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full w-3/5 transition-all duration-500\" />\n                </div>\n                \n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-foreground/70\">XP Earned</span>\n                  <span className=\"text-accent font-medium\">+250</span>\n                </div>\n                \n                <div className=\"flex items-center justify-between text-sm\">\n                  <span className=\"text-foreground/70\">Streak</span>\n                  <div className=\"flex items-center space-x-1\">\n                    <span className=\"text-secondary font-medium\">7 days</span>\n                    <div className=\"w-2 h-2 bg-secondary rounded-full animate-pulse\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </aside>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AAjBA;;;;;AAwBA,MAAM,kBAAkB;IACtB;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,mOAAU;YAAC;YAC1D;gBAAE,MAAM;gBAAW,MAAM;gBAAY,MAAM,6NAAQ;YAAC;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAW,MAAM,6MAAI;YAAC;YACnD;gBAAE,MAAM;gBAAmB,MAAM;gBAAoB,MAAM,yNAAQ;YAAC;SACrE;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM;gBAAiB,MAAM,gNAAK;YAAC;YAC3D;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,mNAAM;YAAC;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAgB,MAAM,6MAAI;YAAC;YACxD;gBAAE,MAAM;gBAAe,MAAM;gBAAQ,MAAM,gNAAK;YAAC;SAClD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAgB,MAAM,mNAAM;YAAC;YAC1D;gBAAE,MAAM;gBAAa,MAAM;gBAAc,MAAM,gNAAK;YAAC;SACtD;IACH;CACD;AAEc,SAAS,QAAQ,KAAwC;QAAxC,EAAE,SAAS,IAAI,EAAE,OAAO,EAAgB,GAAxC;;IAC9B,MAAM,WAAW,IAAA,oJAAW;IAE5B,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBAAM,WAAW,IAAA,4HAAE,EAClB,gJACA,SAAS,kBAAkB;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;gDAClB,MAAM,WAAW,aAAa,KAAK,IAAI;gDACvC,MAAM,OAAO,KAAK,IAAI;gDAEtB,qBACE,6LAAC;8DACC,cAAA,6LAAC,0KAAI;wDACH,MAAM,KAAK,IAAI;wDACf,SAAS;wDACT,WAAW,kIACT,sFACA,WACI,qEACA;;0EAGN,6LAAC;gEAAK,WAAW,kIACf,6BACA,WAAW,iBAAiB;;;;;;0EAE9B,6LAAC;gEAAK,WAAU;0EAAe,KAAK,IAAI;;;;;;4DACvC,0BACC,6LAAC;gEAAI,WAAU;;;;;;;;;;;;mDAjBZ,KAAK,IAAI;;;;;4CAsBtB;;;;;;;mCAhCM,QAAQ,KAAK;;;;;;;;;;sCAuC3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;kEACrC,6LAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;;0DAE7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAGjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;kEACrC,6LAAC;wDAAK,WAAU;kEAA0B;;;;;;;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAqB;;;;;;kEACrC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA6B;;;;;;0EAC7C,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnC;GA5FwB;;QACL,oJAAW;;;KADN", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/code/vibe-online-code-learn-app/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Menu, X } from 'lucide-react';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\nimport { cn } from '@/lib/utils';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n  user?: {\n    username: string;\n    level: number;\n    xp: number;\n    avatar?: string;\n  };\n  showSidebar?: boolean;\n}\n\nexport default function MainLayout({ \n  children, \n  user, \n  showSidebar = true \n}: MainLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);\n  const closeSidebar = () => setSidebarOpen(false);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-background to-background/90\">\n      {/* Header */}\n      <Header user={user} />\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        {showSidebar && (\n          <Sidebar \n            isOpen={sidebarOpen} \n            onClose={closeSidebar}\n          />\n        )}\n\n        {/* Main Content */}\n        <main className={cn(\n          \"flex-1 transition-all duration-300\",\n          showSidebar ? \"lg:ml-64\" : \"\"\n        )}>\n          {/* Mobile Menu Button */}\n          {showSidebar && (\n            <button\n              onClick={toggleSidebar}\n              className=\"lg:hidden fixed top-20 left-4 z-40 p-2 glass rounded-lg hover:glow-primary transition-all duration-200\"\n            >\n              {sidebarOpen ? (\n                <X className=\"w-5 h-5 text-foreground\" />\n              ) : (\n                <Menu className=\"w-5 h-5 text-foreground\" />\n              )}\n            </button>\n          )}\n\n          {/* Content Area */}\n          <div className=\"p-6 min-h-[calc(100vh-4rem)]\">\n            {children}\n          </div>\n        </main>\n      </div>\n\n      {/* Background Effects */}\n      <div className=\"fixed inset-0 -z-10 overflow-hidden pointer-events-none\">\n        {/* Animated Background Orbs */}\n        <div className=\"absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl animate-float\" />\n        <div className=\"absolute top-3/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl animate-float\" style={{ animationDelay: '2s' }} />\n        <div className=\"absolute top-1/2 left-3/4 w-48 h-48 bg-secondary/5 rounded-full blur-3xl animate-float\" style={{ animationDelay: '4s' }} />\n        \n        {/* Grid Pattern */}\n        <div \n          className=\"absolute inset-0 opacity-[0.02]\"\n          style={{\n            backgroundImage: `\n              linear-gradient(rgba(184, 187, 38, 0.1) 1px, transparent 1px),\n              linear-gradient(90deg, rgba(184, 187, 38, 0.1) 1px, transparent 1px)\n            `,\n            backgroundSize: '50px 50px'\n          }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAmBe,SAAS,WAAW,KAIjB;QAJiB,EACjC,QAAQ,EACR,IAAI,EACJ,cAAc,IAAI,EACF,GAJiB;;IAKjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,yKAAQ,EAAC;IAE/C,MAAM,gBAAgB,IAAM,eAAe,CAAC;IAC5C,MAAM,eAAe,IAAM,eAAe;IAE1C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,oJAAM;gBAAC,MAAM;;;;;;0BAEd,6LAAC;gBAAI,WAAU;;oBAEZ,6BACC,6LAAC,qJAAO;wBACN,QAAQ;wBACR,SAAS;;;;;;kCAKb,6LAAC;wBAAK,WAAW,IAAA,4HAAE,EACjB,sCACA,cAAc,aAAa;;4BAG1B,6BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET,4BACC,6LAAC,oMAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,6MAAI;oCAAC,WAAU;;;;;;;;;;;0CAMtB,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAuF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACpI,6LAAC;wBAAI,WAAU;wBAAyF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAGtI,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAkB;4BAIlB,gBAAgB;wBAClB;;;;;;;;;;;;;;;;;;AAKV;GAvEwB;KAAA", "debugId": null}}]}