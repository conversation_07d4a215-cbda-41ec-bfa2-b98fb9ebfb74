import React from 'react';
import { BookOpen, Trophy, Target, Calendar, TrendingUp, Zap } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Progress } from '@/components/ui/Progress';
import { Button } from '@/components/ui/Button';

// Mock user data
const mockUser = {
  username: 'CodeMaster',
  level: 12,
  xp: 14500,
  avatar: undefined,
};

const stats = [
  {
    title: 'Lessons Completed',
    value: '47',
    change: '+12%',
    icon: BookOpen,
    color: 'text-primary',
  },
  {
    title: 'Current Streak',
    value: '7 days',
    change: '+2 days',
    icon: Zap,
    color: 'text-secondary',
  },
  {
    title: 'Achievements',
    value: '23',
    change: '+3 new',
    icon: Trophy,
    color: 'text-accent',
  },
  {
    title: 'Rank',
    value: '#156',
    change: '+24',
    icon: Target,
    color: 'text-destructive',
  },
];

const recentActivity = [
  {
    title: 'Completed Python Basics - Variables',
    time: '2 hours ago',
    xp: '+50 XP',
    type: 'lesson',
  },
  {
    title: 'Earned "First Steps" Achievement',
    time: '3 hours ago',
    xp: '+100 XP',
    type: 'achievement',
  },
  {
    title: 'Solved Daily Challenge',
    time: '1 day ago',
    xp: '+75 XP',
    type: 'challenge',
  },
];

const currentCourses = [
  {
    title: 'Python Fundamentals',
    progress: 75,
    nextLesson: 'Functions and Scope',
    language: 'Python',
    icon: '🐍',
  },
  {
    title: 'C# Basics',
    progress: 45,
    nextLesson: 'Object-Oriented Programming',
    language: 'C#',
    icon: '🔷',
  },
  {
    title: 'Node.js Development',
    progress: 30,
    nextLesson: 'Express.js Fundamentals',
    language: 'Node.js',
    icon: '🟢',
  },
];

export default function Dashboard() {
  return (
    <MainLayout user={mockUser}>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-foreground">
            Welcome back, {mockUser.username}! 👋
          </h1>
          <p className="text-foreground/70">
            Ready to continue your coding journey? Let's see what you can accomplish today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} variant="glass" className="hover:glow-primary transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-foreground/70">
                    {stat.title}
                  </CardTitle>
                  <Icon className={`w-4 h-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                  <p className="text-xs text-primary font-medium">
                    {stat.change} from last week
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Current Courses */}
          <div className="lg:col-span-2 space-y-6">
            <Card variant="glass-strong">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5 text-primary" />
                  <span>Continue Learning</span>
                </CardTitle>
                <CardDescription>
                  Pick up where you left off in your courses
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {currentCourses.map((course, index) => (
                  <div key={index} className="glass p-4 rounded-lg hover:glow-accent transition-all duration-300">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{course.icon}</span>
                        <div>
                          <h4 className="font-semibold text-foreground">{course.title}</h4>
                          <p className="text-sm text-foreground/60">Next: {course.nextLesson}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        Continue
                      </Button>
                    </div>
                    <Progress 
                      value={course.progress} 
                      variant="gradient" 
                      showLabel 
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="space-y-6">
            <Card variant="glass-strong">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-accent" />
                  <span>Recent Activity</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 glass rounded-lg">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      activity.type === 'lesson' ? 'bg-primary' :
                      activity.type === 'achievement' ? 'bg-secondary' : 'bg-accent'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground truncate">
                        {activity.title}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <p className="text-xs text-foreground/60">{activity.time}</p>
                        <span className="text-xs font-medium text-primary">{activity.xp}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Daily Challenge */}
            <Card variant="glass-strong" className="glow-secondary">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5 text-secondary" />
                  <span>Daily Challenge</span>
                </CardTitle>
                <CardDescription>
                  Complete today's challenge for bonus XP!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="glass p-4 rounded-lg">
                    <h4 className="font-semibold text-foreground mb-2">
                      Python List Comprehensions
                    </h4>
                    <p className="text-sm text-foreground/70 mb-3">
                      Create a list comprehension that filters even numbers from 1 to 100.
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-secondary font-medium">+150 XP</span>
                      <Button size="sm" variant="gradient">
                        Start Challenge
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
