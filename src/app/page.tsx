import React from 'react';
import <PERSON> from 'next/link';
import { ArrowRight, Code, Trophy, Users, Zap, BookOpen, Target } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Progress } from '@/components/ui/Progress';

// Mock user data for demonstration
const mockUser = {
  username: 'CodeMaster',
  level: 12,
  xp: 14500,
  avatar: undefined,
};

const features = [
  {
    icon: Code,
    title: 'Interactive Code Editor',
    description: 'Write, test, and execute code directly in your browser with real-time feedback.',
    color: 'text-primary',
  },
  {
    icon: Trophy,
    title: 'Gamified Learning',
    description: 'Earn XP, unlock achievements, and collect trophies as you progress through lessons.',
    color: 'text-secondary',
  },
  {
    icon: Users,
    title: 'Community Challenges',
    description: 'Compete with other learners in daily challenges and climb the leaderboards.',
    color: 'text-accent',
  },
  {
    icon: Zap,
    title: 'Virtual Pet Companion',
    description: 'Grow and care for your coding companion that evolves with your learning journey.',
    color: 'text-destructive',
  },
];

const languages = [
  { name: 'Python', icon: '🐍', progress: 75, color: 'from-green-500 to-blue-500' },
  { name: 'C#', icon: '🔷', progress: 45, color: 'from-purple-500 to-pink-500' },
  { name: 'Node.js', icon: '🟢', progress: 60, color: 'from-green-400 to-emerald-500' },
];

export default function Home() {
  return (
    <MainLayout user={mockUser} showSidebar={false}>
      <div className="max-w-7xl mx-auto">
        {/* Hero Section */}
        <section className="text-center py-20">
          <div className="space-y-6">
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
              Learn to Code
            </h1>
            <h2 className="text-3xl md:text-4xl font-semibold text-foreground/90">
              with <span className="text-primary">VibeCode</span>
            </h2>
            <p className="text-xl text-foreground/70 max-w-2xl mx-auto leading-relaxed">
              Master backend programming through interactive lessons, gamified challenges,
              and a virtual pet companion that grows with your coding skills.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
              <Button size="xl" variant="gradient" asChild>
                <Link href="/courses" className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5" />
                  <span>Start Learning</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </Button>
              <Button size="xl" variant="glass" asChild>
                <Link href="/challenges" className="flex items-center space-x-2">
                  <Target className="w-5 h-5" />
                  <span>Try Challenge</span>
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Why Choose VibeCode?
            </h3>
            <p className="text-foreground/70 text-lg max-w-2xl mx-auto">
              Experience a new way of learning programming with our innovative features
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} variant="glass" className="hover:glow-primary transition-all duration-300 group">
                  <CardHeader className="text-center">
                    <div className={`w-12 h-12 mx-auto mb-4 rounded-lg bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`w-6 h-6 ${feature.color}`} />
                    </div>
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-center">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* Languages Section */}
        <section className="py-20">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-foreground mb-4">
              Master Backend Technologies
            </h3>
            <p className="text-foreground/70 text-lg max-w-2xl mx-auto">
              Learn the most in-demand backend programming languages with hands-on projects
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {languages.map((lang, index) => (
              <Card key={index} variant="glass-strong" className="hover:glow-accent transition-all duration-300">
                <CardHeader className="text-center">
                  <div className="text-4xl mb-4">{lang.icon}</div>
                  <CardTitle className="text-xl">{lang.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Progress
                    value={lang.progress}
                    variant="gradient"
                    showLabel
                    label="Course Progress"
                  />
                  <Button variant="outline" className="w-full">
                    Continue Learning
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 text-center">
          <Card variant="gradient" className="p-12 glow-primary">
            <div className="space-y-6">
              <h3 className="text-3xl font-bold text-foreground">
                Ready to Start Your Coding Journey?
              </h3>
              <p className="text-foreground/80 text-lg max-w-2xl mx-auto">
                Join thousands of learners who are mastering backend development with VibeCode
              </p>
              <Button size="xl" variant="glass" asChild>
                <Link href="/sign-up" className="flex items-center space-x-2">
                  <span>Get Started Free</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
              </Button>
            </div>
          </Card>
        </section>
      </div>
    </MainLayout>
  );
}
