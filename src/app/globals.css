@import "tailwindcss";

:root {
  /* Gruvbox Dark Glass Theme */
  --background: rgba(40, 40, 40, 0.95);
  --foreground: #ebdbb2;
  --primary: #b8bb26;
  --secondary: #fabd2f;
  --accent: #83a598;
  --destructive: #fb4934;
  --muted: rgba(60, 56, 54, 0.8);
  --border: rgba(168, 153, 132, 0.3);
  --card: rgba(50, 48, 47, 0.9);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

body {
  background: linear-gradient(135deg, #282828 0%, #1d2021 100%);
  color: var(--foreground);
  min-height: 100vh;
  position: relative;
}

/* Glass morphism effect */
.glass {
  background: rgba(50, 48, 47, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(168, 153, 132, 0.2);
  border-radius: 12px;
}

.glass-strong {
  background: rgba(50, 48, 47, 0.9);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(168, 153, 132, 0.3);
  border-radius: 12px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(60, 56, 54, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(184, 187, 38, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(184, 187, 38, 0.8);
}

/* Glow effects */
.glow-primary {
  box-shadow: 0 0 20px rgba(184, 187, 38, 0.3);
}

.glow-accent {
  box-shadow: 0 0 20px rgba(131, 165, 152, 0.3);
}

/* Animation utilities */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}
